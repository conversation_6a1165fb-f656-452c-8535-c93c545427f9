<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PromptController;
use App\Http\Controllers\SystemController;
use App\Http\Controllers\SubjectController;
use App\Http\Controllers\LlmModelController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Rotas do sistema
Route::prefix('system')->group(function () {
    Route::get('/status', [SystemController::class, 'status'])->name('system.status');
    Route::get('/models', [SystemController::class, 'models'])->name('system.models');
    Route::get('/stats', [SystemController::class, 'stats'])->name('system.stats');
    Route::get('/queue/jobs', [SystemController::class, 'queueJobs'])->name('system.queue.jobs');
    Route::delete('/queue/jobs/{jobId}', [SystemController::class, 'deleteQueueJob'])->name('system.queue.jobs.destroy');
    Route::post('/sync-models', [SystemController::class, 'syncModels'])->name('system.sync-models');
    Route::get('/model-sync-stats', [SystemController::class, 'modelSyncStats'])->name('system.model-sync-stats');

    // Rotas específicas do dashboard (requerem autenticação)
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/dashboard', [SystemController::class, 'dashboardData'])->name('system.dashboard');
        Route::get('/queue/user-jobs', [SystemController::class, 'userQueueJobs'])->name('system.queue.user-jobs');
        Route::get('/stats/model-usage', [SystemController::class, 'modelUsageStats'])->name('system.stats.model-usage');
        Route::get('/stats/queue-history', [SystemController::class, 'queueHistoryStats'])->name('system.stats.queue-history');
    });
});

// Rotas para o sistema de prompts
Route::prefix('prompts')->middleware('auth:sanctum')->group(function () {
    Route::get('/', [PromptController::class, 'index'])->name('prompts.index');
    Route::post('/', [PromptController::class, 'store'])->name('prompts.store');
    Route::get('/{prompt}', [PromptController::class, 'show'])->name('prompts.show');
    Route::put('/{prompt}', [PromptController::class, 'update'])->name('prompts.update');
    Route::delete('/{prompt}', [PromptController::class, 'destroy'])->name('prompts.destroy');
    Route::get('/{prompt}/status', [PromptController::class, 'status'])->name('prompts.status');
    Route::get('/{prompt}/responses', [PromptController::class, 'responses'])->name('prompts.responses');
    Route::delete('/{prompt}/responses/{responseUuid}', [PromptController::class, 'destroyResponse'])->name('prompts.responses.destroy');
    Route::post('/{prompt}/jobs', [PromptController::class, 'createJob'])->name('prompts.jobs.create');
});

// Rotas para assuntos (requer autenticação)
Route::middleware('auth:sanctum')->prefix('subjects')->group(function () {
    // Rotas RESTful básicas
    Route::get('/', [SubjectController::class, 'index'])->name('subjects.index');
    Route::post('/', [SubjectController::class, 'store'])->name('subjects.store');
    Route::get('/{subject}', [SubjectController::class, 'show'])->name('subjects.show');
    Route::put('/{subject}', [SubjectController::class, 'update'])->name('subjects.update');
    Route::patch('/{subject}', [SubjectController::class, 'update'])->name('subjects.patch');
    Route::delete('/{subject}', [SubjectController::class, 'destroy'])->name('subjects.destroy');

    // Rotas específicas para estrutura de árvore
    Route::get('/roots/list', [SubjectController::class, 'roots'])->name('subjects.roots');
    Route::get('/{subject}/children', [SubjectController::class, 'children'])->name('subjects.children');
});

// Rotas para modelos LLM (requer autenticação)
Route::middleware('auth:sanctum')->prefix('llm-models')->group(function () {
    // Rotas RESTful básicas
    Route::get('/', [LlmModelController::class, 'index'])->name('llm-models.index');
    Route::post('/', [LlmModelController::class, 'store'])->name('llm-models.store');
    Route::get('/{llmModel}', [LlmModelController::class, 'show'])->name('llm-models.show');
    Route::put('/{llmModel}', [LlmModelController::class, 'update'])->name('llm-models.update');
    Route::patch('/{llmModel}', [LlmModelController::class, 'update'])->name('llm-models.patch');
    Route::delete('/{llmModel}', [LlmModelController::class, 'destroy'])->name('llm-models.destroy');

    // Rotas específicas para metadados
    Route::post('/{llmModel}/refresh-metadata', [LlmModelController::class, 'refreshMetadata'])->name('llm-models.refresh-metadata');
    Route::post('/bulk-refresh-metadata', [LlmModelController::class, 'bulkRefreshMetadata'])->name('llm-models.bulk-refresh-metadata');

    // Rotas para estatísticas e filtros
    Route::get('/statistics/overview', [LlmModelController::class, 'statistics'])->name('llm-models.statistics');
    Route::get('/filter-options/list', [LlmModelController::class, 'filterOptions'])->name('llm-models.filter-options');
});
