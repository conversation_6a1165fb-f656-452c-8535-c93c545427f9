<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LlmModelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'name' => $this->name,
            'display_name' => $this->display_name,
            'description' => $this->description,
            
            // Hugging Face metadata
            'huggingface_id' => $this->huggingface_id,
            'huggingface_url' => $this->huggingface_url,
            'huggingface_metadata' => $this->when(
                $request->has('include_raw_metadata'),
                $this->huggingface_metadata
            ),
            
            // Capabilities
            'capabilities' => [
                'reasoning' => $this->has_reasoning,
                'tool_usage' => $this->has_tool_usage,
                'vision' => $this->has_vision,
                'code_generation' => $this->has_code_generation,
                'function_calling' => $this->has_function_calling,
            ],
            'capabilities_list' => $this->capabilities,
            
            // Technical specifications
            'specifications' => [
                'context_window' => $this->context_window,
                'parameter_count' => $this->parameter_count,
                'parameter_count_numeric' => $this->parameter_count_numeric,
                'architecture' => $this->architecture,
                'quantization' => $this->quantization,
                'format' => $this->format,
            ],
            
            // Chat and prompt formatting
            'formatting' => [
                'chat_template' => $this->chat_template,
                'prompt_templates' => $this->prompt_templates,
            ],
            
            // Performance and resource requirements
            'performance' => [
                'model_size_bytes' => $this->model_size_bytes,
                'model_size_formatted' => $this->formatted_size,
                'estimated_vram_gb' => $this->estimated_vram_gb,
                'benchmark_score' => $this->benchmark_score,
                'benchmark_details' => $this->benchmark_details,
            ],
            
            // Licensing and usage
            'licensing' => [
                'license' => $this->license,
                'commercial_use' => $this->commercial_use,
            ],
            
            'tags' => $this->tags ?? [],
            'status' => $this->status,
            
            // Metadata and timestamps
            'metadata' => [
                'last_crawled_at' => $this->last_crawled_at?->toISOString(),
                'huggingface_updated_at' => $this->huggingface_updated_at,
                'crawl_metadata' => $this->crawl_metadata,
                'needs_update' => $this->needsMetadataUpdate(),
            ],
            
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relationships
            'responses_count' => $this->whenCounted('responses'),
            'recent_responses' => $this->whenLoaded('responses', function () {
                return $this->responses->map(function ($response) {
                    return [
                        'uuid' => $response->uuid,
                        'prompt_id' => $response->prompt_id,
                        'status' => $response->status,
                        'generated_at' => $response->generated_at?->toISOString(),
                        'processing_time' => $response->metadata['processing_time'] ?? null,
                    ];
                });
            }),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'resource_type' => 'llm_model',
                'version' => '1.0',
            ],
        ];
    }
}
