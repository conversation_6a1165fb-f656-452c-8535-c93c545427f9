<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLlmModelRequest;
use App\Http\Requests\UpdateLlmModelRequest;
use App\Http\Resources\LlmModelResource;
use App\Jobs\CrawlModelMetadataJob;
use App\Models\LlmModel;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LlmModelController extends Controller
{
    /**
     * Display a listing of LLM models
     */
    public function index(Request $request): JsonResponse
    {
        $query = LlmModel::query();

        // Apply filters
        if ($request->has('status') && $request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('architecture') && $request->filled('architecture')) {
            $query->byArchitecture($request->architecture);
        }

        if ($request->has('capability') && $request->filled('capability')) {
            $query->withCapability($request->capability);
        }

        if ($request->has('min_params')) {
            $query->byParameterRange($request->min_params);
        }

        if ($request->has('max_params')) {
            $query->byParameterRange(null, $request->max_params);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('architecture', 'like', "%{$search}%");
            });
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'name', 'display_name', 'parameter_count_numeric', 'last_crawled_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $models = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => LlmModelResource::collection($models->items()),
            'meta' => [
                'current_page' => $models->currentPage(),
                'last_page' => $models->lastPage(),
                'per_page' => $models->perPage(),
                'total' => $models->total(),
                'from' => $models->firstItem(),
                'to' => $models->lastItem(),
            ],
        ]);
    }

    /**
     * Store a new LLM model
     */
    public function store(StoreLlmModelRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Parse parameter count to numeric if provided
        if (isset($validated['parameter_count'])) {
            $validated['parameter_count_numeric'] = LlmModel::parseParameterCount($validated['parameter_count']);
        }

        // Extract Hugging Face ID if not provided
        if (!isset($validated['huggingface_id']) && isset($validated['name'])) {
            $validated['huggingface_id'] = LlmModel::extractHuggingFaceId($validated['name']);
        }

        $model = LlmModel::create($validated);

        // Dispatch crawling job if Hugging Face ID is available
        if ($model->huggingface_id) {
            CrawlModelMetadataJob::dispatch($model->name, true);
            Log::info("Dispatched metadata crawl job for new model: {$model->name}");
        }

        return response()->json([
            'success' => true,
            'data' => new LlmModelResource($model),
            'message' => 'LLM model created successfully. Metadata crawling has been queued.',
        ], 201);
    }

    /**
     * Display the specified LLM model
     */
    public function show(LlmModel $llmModel): JsonResponse
    {
        $llmModel->load(['responses' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return response()->json([
            'success' => true,
            'data' => new LlmModelResource($llmModel),
        ]);
    }

    /**
     * Update the specified LLM model
     */
    public function update(UpdateLlmModelRequest $request, LlmModel $llmModel): JsonResponse
    {
        $validated = $request->validated();

        // Parse parameter count to numeric if provided
        if (isset($validated['parameter_count'])) {
            $validated['parameter_count_numeric'] = LlmModel::parseParameterCount($validated['parameter_count']);
        }

        $llmModel->update($validated);

        return response()->json([
            'success' => true,
            'data' => new LlmModelResource($llmModel),
            'message' => 'LLM model updated successfully.',
        ]);
    }

    /**
     * Remove the specified LLM model
     */
    public function destroy(LlmModel $llmModel): JsonResponse
    {
        $modelName = $llmModel->name;
        $llmModel->delete();

        Log::info("LLM model deleted: {$modelName}");

        return response()->json([
            'success' => true,
            'message' => 'LLM model deleted successfully.',
        ]);
    }

    /**
     * Refresh metadata for the specified model
     */
    public function refreshMetadata(LlmModel $llmModel): JsonResponse
    {
        if (!$llmModel->huggingface_id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot refresh metadata: No Hugging Face ID available.',
            ], 422);
        }

        CrawlModelMetadataJob::dispatch($llmModel->name, true);

        Log::info("Dispatched metadata refresh job for model: {$llmModel->name}");

        return response()->json([
            'success' => true,
            'message' => 'Metadata refresh has been queued.',
        ]);
    }

    /**
     * Bulk refresh metadata for multiple models
     */
    public function bulkRefreshMetadata(Request $request): JsonResponse
    {
        $request->validate([
            'model_ids' => 'required|array',
            'model_ids.*' => 'exists:llm_models,uuid',
        ]);

        $models = LlmModel::whereIn('uuid', $request->model_ids)
            ->whereNotNull('huggingface_id')
            ->get();

        $dispatched = 0;
        foreach ($models as $model) {
            CrawlModelMetadataJob::dispatch($model->name, true);
            $dispatched++;
        }

        Log::info("Dispatched bulk metadata refresh for {$dispatched} models");

        return response()->json([
            'success' => true,
            'message' => "Metadata refresh queued for {$dispatched} models.",
        ]);
    }

    /**
     * Get model statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_models' => LlmModel::count(),
            'active_models' => LlmModel::where('status', 'active')->count(),
            'models_with_metadata' => LlmModel::whereNotNull('last_crawled_at')->count(),
            'models_needing_update' => LlmModel::get()->filter->needsMetadataUpdate()->count(),
            'architectures' => LlmModel::whereNotNull('architecture')
                ->groupBy('architecture')
                ->selectRaw('architecture, count(*) as count')
                ->pluck('count', 'architecture'),
            'capabilities' => [
                'reasoning' => LlmModel::where('has_reasoning', true)->count(),
                'tool_usage' => LlmModel::where('has_tool_usage', true)->count(),
                'vision' => LlmModel::where('has_vision', true)->count(),
                'code_generation' => LlmModel::where('has_code_generation', true)->count(),
                'function_calling' => LlmModel::where('has_function_calling', true)->count(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get available filter options
     */
    public function filterOptions(): JsonResponse
    {
        $options = [
            'architectures' => LlmModel::whereNotNull('architecture')
                ->distinct()
                ->pluck('architecture')
                ->sort()
                ->values(),
            'capabilities' => [
                'reasoning' => 'Reasoning',
                'tool_usage' => 'Tool Usage',
                'vision' => 'Vision',
                'code_generation' => 'Code Generation',
                'function_calling' => 'Function Calling',
            ],
            'statuses' => ['active', 'inactive', 'deprecated'],
            'parameter_ranges' => [
                ['label' => 'Small (< 1B)', 'min' => 0, 'max' => 999999999],
                ['label' => 'Medium (1B - 10B)', 'min' => 1000000000, 'max' => 9999999999],
                ['label' => 'Large (10B - 100B)', 'min' => 10000000000, 'max' => 99999999999],
                ['label' => 'Extra Large (> 100B)', 'min' => 100000000000, 'max' => null],
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $options,
        ]);
    }
}
