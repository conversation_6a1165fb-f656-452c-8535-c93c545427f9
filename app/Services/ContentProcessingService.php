<?php

namespace App\Services;

class ContentProcessingService
{
    /**
     * Process response content to separate reasoning from main content
     *
     * @param string $content The full response content
     * @return array{reasoning: string|null, content: string}
     */
    public function processResponseContent(string $content): array
    {
        if (empty($content)) {
            return [
                'reasoning' => null,
                'content' => ''
            ];
        }

        // Regular expression to match <think> tags and their content
        $thinkRegex = '/<think>([\s\S]*?)<\/think>/i';
        $matches = [];
        
        // Find all <think> blocks
        preg_match_all($thinkRegex, $content, $matches, PREG_SET_ORDER);

        if (empty($matches)) {
            // No reasoning content found
            return [
                'reasoning' => null,
                'content' => trim($content)
            ];
        }

        // Extract all reasoning content
        $reasoningParts = [];
        foreach ($matches as $match) {
            $reasoningParts[] = trim($match[1]);
        }
        
        // Combine all reasoning parts
        $reasoning = implode("\n\n", array_filter($reasoningParts));

        // Remove all <think> blocks from the main content
        $mainContent = preg_replace($thinkRegex, '', $content);

        // Clean up extra whitespace and normalize line breaks
        $mainContent = preg_replace('/\n\s*\n\s*\n/', "\n\n", $mainContent);
        $mainContent = trim($mainContent);

        return [
            'reasoning' => !empty($reasoning) ? $reasoning : null,
            'content' => $mainContent
        ];
    }

    /**
     * Check if content contains reasoning tags
     *
     * @param string $content
     * @return bool
     */
    public function hasReasoningTags(string $content): bool
    {
        return preg_match('/<think>[\s\S]*?<\/think>/i', $content) === 1;
    }

    /**
     * Reconstruct full content from separated reasoning and content
     * (for backward compatibility or export purposes)
     *
     * @param string|null $reasoning
     * @param string $content
     * @return string
     */
    public function reconstructFullContent(?string $reasoning, string $content): string
    {
        if (empty($reasoning)) {
            return $content;
        }

        return "<think>\n{$reasoning}\n</think>\n\n{$content}";
    }

    /**
     * Validate that reasoning content doesn't contain nested think tags
     *
     * @param string $reasoning
     * @return bool
     */
    public function validateReasoningContent(string $reasoning): bool
    {
        // Check for nested <think> tags which shouldn't exist in extracted reasoning
        return !preg_match('/<think>|<\/think>/i', $reasoning);
    }
}
