<?php

namespace App\Services;

use App\Jobs\CrawlModelMetadataJob;
use App\Models\LlmModel;
use App\Services\AI\OllamaService;
use Illuminate\Support\Facades\Log;

class ModelSyncService
{
    public function __construct(
        private OllamaService $ollamaService
    ) {}

    /**
     * Sync models from Ollama with the LLM models database
     */
    public function syncModels(): array
    {
        $stats = [
            'total_ollama_models' => 0,
            'new_models_created' => 0,
            'existing_models' => 0,
            'crawl_jobs_dispatched' => 0,
            'errors' => [],
        ];

        try {
            // Get available models from Ollama
            $ollamaModels = $this->ollamaService->getAvailableModels();
            $stats['total_ollama_models'] = count($ollamaModels);

            Log::info("Starting model sync", [
                'ollama_models_count' => $stats['total_ollama_models'],
                'models' => array_keys($ollamaModels)
            ]);

            foreach ($ollamaModels as $modelName => $displayName) {
                try {
                    $result = $this->syncSingleModel($modelName, $displayName);
                    
                    if ($result['created']) {
                        $stats['new_models_created']++;
                        if ($result['crawl_dispatched']) {
                            $stats['crawl_jobs_dispatched']++;
                        }
                    } else {
                        $stats['existing_models']++;
                    }
                } catch (\Exception $e) {
                    $error = "Failed to sync model {$modelName}: " . $e->getMessage();
                    $stats['errors'][] = $error;
                    Log::error($error, ['exception' => $e]);
                }
            }

            Log::info("Model sync completed", $stats);

        } catch (\Exception $e) {
            $error = "Failed to get models from Ollama: " . $e->getMessage();
            $stats['errors'][] = $error;
            Log::error($error, ['exception' => $e]);
        }

        return $stats;
    }

    /**
     * Sync a single model
     */
    private function syncSingleModel(string $modelName, string $displayName): array
    {
        $result = [
            'created' => false,
            'crawl_dispatched' => false,
        ];

        // Check if model already exists
        $existingModel = LlmModel::where('name', $modelName)->first();
        
        if ($existingModel) {
            Log::debug("Model {$modelName} already exists, skipping");
            return $result;
        }

        // Create new LLM model instance
        $llmModel = $this->createLlmModelFromOllama($modelName, $displayName);
        $result['created'] = true;

        Log::info("Created new LLM model: {$modelName}", [
            'uuid' => $llmModel->uuid,
            'display_name' => $llmModel->display_name,
            'huggingface_id' => $llmModel->huggingface_id,
        ]);

        // Dispatch crawling job if Hugging Face ID is available
        if ($llmModel->huggingface_id) {
            CrawlModelMetadataJob::dispatch($llmModel->name, false);
            $result['crawl_dispatched'] = true;
            
            Log::info("Dispatched metadata crawl job for: {$modelName}");
        }

        return $result;
    }

    /**
     * Create LLM model from Ollama model data
     */
    private function createLlmModelFromOllama(string $modelName, string $displayName): LlmModel
    {
        // Extract Hugging Face ID if possible
        $huggingfaceId = LlmModel::extractHuggingFaceId($modelName);
        
        // Generate display name if not provided or same as model name
        if (!$displayName || $displayName === $modelName) {
            $displayName = $this->generateDisplayName($modelName);
        }

        // Create the model with basic information
        $modelData = [
            'name' => $modelName,
            'display_name' => $displayName,
            'description' => "Model automatically imported from Ollama",
            'status' => 'active',
        ];

        // Add Hugging Face information if available
        if ($huggingfaceId) {
            $modelData['huggingface_id'] = $huggingfaceId;
            $modelData['huggingface_url'] = "https://huggingface.co/{$huggingfaceId}";
        }

        // Try to extract basic technical specs from model name
        $specs = $this->extractBasicSpecs($modelName);
        $modelData = array_merge($modelData, $specs);

        return LlmModel::create($modelData);
    }

    /**
     * Generate a display name from model name
     */
    private function generateDisplayName(string $modelName): string
    {
        // Remove "hf.co/" prefix if present
        $name = str_replace('hf.co/', '', $modelName);
        
        // Split by colon to separate model from quantization
        $parts = explode(':', $name);
        $baseName = $parts[0];
        $quantization = $parts[1] ?? null;
        
        // Extract the model name part (after the last slash)
        $nameParts = explode('/', $baseName);
        $modelPart = end($nameParts);
        
        // Clean up the name
        $displayName = str_replace(['-', '_'], ' ', $modelPart);
        $displayName = ucwords($displayName);
        
        // Add quantization if present
        if ($quantization) {
            $displayName .= " ({$quantization})";
        }
        
        return $displayName;
    }

    /**
     * Extract basic specifications from model name
     */
    private function extractBasicSpecs(string $modelName): array
    {
        $specs = [];
        $modelNameLower = strtolower($modelName);

        // Extract architecture
        $architectures = ['llama', 'qwen', 'mistral', 'gemma', 'phi', 'codellama', 'deepseek'];
        foreach ($architectures as $arch) {
            if (str_contains($modelNameLower, $arch)) {
                $specs['architecture'] = ucfirst($arch);
                break;
            }
        }

        // Extract parameter count
        if (preg_match('/(\d+(?:\.\d+)?[kmbt]?)(?:-|_|\s)/i', $modelName, $matches)) {
            $specs['parameter_count'] = strtoupper($matches[1]);
            $specs['parameter_count_numeric'] = LlmModel::parseParameterCount($matches[1]);
        }

        // Extract quantization
        if (preg_match('/(Q\d+_[A-Z0-9]+|FP16|FP32|INT8|INT4)/i', $modelName, $matches)) {
            $specs['quantization'] = strtoupper($matches[1]);
        }

        // Extract format
        if (str_contains($modelNameLower, 'gguf')) {
            $specs['format'] = 'GGUF';
        }

        // Set some basic capabilities based on model name patterns
        if (str_contains($modelNameLower, 'code') || str_contains($modelNameLower, 'coder')) {
            $specs['has_code_generation'] = true;
        }

        if (str_contains($modelNameLower, 'vision') || str_contains($modelNameLower, 'visual')) {
            $specs['has_vision'] = true;
        }

        return $specs;
    }

    /**
     * Get sync statistics
     */
    public function getSyncStats(): array
    {
        $totalModels = LlmModel::count();
        $activeModels = LlmModel::where('status', 'active')->count();
        $modelsWithMetadata = LlmModel::whereNotNull('last_crawled_at')->count();
        $modelsNeedingUpdate = LlmModel::get()->filter->needsMetadataUpdate()->count();

        return [
            'total_llm_models' => $totalModels,
            'active_models' => $activeModels,
            'models_with_metadata' => $modelsWithMetadata,
            'models_needing_update' => $modelsNeedingUpdate,
            'sync_coverage' => $totalModels > 0 ? round(($modelsWithMetadata / $totalModels) * 100, 2) : 0,
        ];
    }

    /**
     * Check if sync is needed
     */
    public function shouldSync(): bool
    {
        try {
            $ollamaModels = $this->ollamaService->getAvailableModels();
            $existingModels = LlmModel::pluck('name')->toArray();
            
            // Check if there are new models in Ollama that don't exist in our database
            $newModels = array_diff(array_keys($ollamaModels), $existingModels);
            
            return count($newModels) > 0;
        } catch (\Exception $e) {
            Log::error("Failed to check if sync is needed: " . $e->getMessage());
            return false;
        }
    }
}
