<?php

namespace App\Services\AI;

use App\Models\LlmModel;
use Illuminate\Support\Facades\Log;

abstract class BaseAIService implements AIServiceInterface
{
    /**
     * Configurações padrão para o serviço.
     *
     * @var array
     */
    protected array $defaultOptions = [];

    /**
     * Nome do serviço.
     *
     * @var string
     */
    protected string $serviceName;

    /**
     * Chave da API.
     *
     * @var string|null
     */
    protected ?string $apiKey;

    /**
     * Modelo padrão a ser usado.
     *
     * @var string
     */
    protected string $defaultModel;

    /**
     * Construtor.
     *
     * @param array $config Configurações adicionais
     */
    public function __construct(array $config = [])
    {
        $this->defaultOptions = array_merge($this->defaultOptions, $config);
    }

    /**
     * {@inheritdoc}
     */
    public function getServiceName(): string
    {
        return $this->serviceName;
    }

    /**
     * {@inheritdoc}
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }

    /**
     * Mescla as opções fornecidas com as opções padrão.
     *
     * @param array $options
     * @return array
     */
    protected function mergeOptions(array $options): array
    {
        return array_merge($this->defaultOptions, $options);
    }

    /**
     * Formata uma mensagem de erro.
     *
     * @param string $message
     * @param mixed $exception
     * @return string
     */
    protected function formatErrorMessage(string $message, $exception = null): string
    {
        $errorMessage = "[{$this->serviceName} Error] {$message}";

        if ($exception && is_object($exception) && method_exists($exception::class, 'getMessage')) {
            $errorMessage .= ": " . $exception->getMessage();
        }

        return $errorMessage;
    }

    /**
     * Registra um erro no log.
     *
     * @param string $message
     * @param mixed $exception
     * @return void
     */
    protected function logError(string $message, $exception = null): void
    {
        if (is_array($exception)) {
            // Se for um array, usar Log::error diretamente com contexto
            Log::error("[{$this->serviceName} Error] {$message}", $exception);
        } else {
            // Se for uma exceção ou string, usar formatErrorMessage
            $errorMessage = $this->formatErrorMessage($message, $exception);
            Log::error($errorMessage);
        }
    }

     /**
     * {@inheritdoc}
     */
    public function sendMessageWithAttachments(string $prompt, array $options = [], array $attachments = []): string
    {
        // Implementação padrão que apenas ignora os anexos
        return $this->sendMessage($prompt, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function sendMessageWithModel(string $prompt, LlmModel $model, array $options = []): string
    {
        // Implementação padrão que usa o modelo como opção
        $modelOptions = $this->extractModelOptions($model);
        $mergedOptions = array_merge($modelOptions, $options);
        return $this->sendMessage($prompt, $mergedOptions);
    }

    /**
     * {@inheritdoc}
     */
    public function sendConversationWithModel(array $messages, LlmModel $model, array $options = []): string
    {
        // Implementação padrão que usa o modelo como opção
        $modelOptions = $this->extractModelOptions($model);
        $mergedOptions = array_merge($modelOptions, $options);
        return $this->sendConversation($messages, $mergedOptions);
    }

    /**
     * {@inheritdoc}
     */
    public function sendMessageWithAttachmentsAndModel(string $prompt, LlmModel $model, array $attachments = [], array $options = []): string
    {
        // Implementação padrão que usa o modelo como opção
        $modelOptions = $this->extractModelOptions($model);
        $mergedOptions = array_merge($modelOptions, $options);
        return $this->sendMessageWithAttachments($prompt, $mergedOptions, $attachments);
    }

    /**
     * Extrai opções de configuração de um LLMModel.
     * Esta implementação padrão pode ser sobrescrita por serviços específicos.
     *
     * @param LlmModel $model
     * @return array
     */
    protected function extractModelOptions(LlmModel $model): array
    {
        $options = [];

        // Usar o nome do modelo
        if ($model->name) {
            $options['model'] = $model->name;
        }

        // Configurações básicas que podem ser aplicadas genericamente
        if ($model->context_window) {
            $options['context_window'] = $model->context_window;
        }

        return $options;
    }
}
