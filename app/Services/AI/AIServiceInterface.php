<?php

namespace App\Services\AI;

use App\Models\LlmModel;

interface AIServiceInterface
{
    /**
     * Envia uma mensagem para o modelo de IA e retorna a resposta.
     *
     * @param string $prompt O prompt ou mensagem a ser enviada
     * @param array $options Opções adicionais para a requisição
     * @return string A resposta do modelo de IA
     */
    public function sendMessage(string $prompt, array $options = []): string;

    /**
     * Envia uma conversa completa para o modelo de IA e retorna a resposta.
     *
     * @param array $messages Array de mensagens no formato [['role' => 'user|assistant|system', 'content' => 'mensagem']]
     * @param array $options Opções adicionais para a requisição
     * @return string A resposta do modelo de IA
     */
    public function sendConversation(array $messages, array $options = []): string;

    /**
     * Envia uma mensagem usando configurações de um LLMModel específico.
     *
     * @param string $prompt O prompt ou mensagem a ser enviada
     * @param LlmModel $model O modelo LLM com configurações específicas
     * @param array $options Opções adicionais para sobrescrever configurações do modelo
     * @return string A resposta do modelo de IA
     */
    public function sendMessageWithModel(string $prompt, LlmModel $model, array $options = []): string;

    /**
     * Envia uma conversa usando configurações de um LLMModel específico.
     *
     * @param array $messages Array de mensagens no formato [['role' => 'user|assistant|system', 'content' => 'mensagem']]
     * @param LlmModel $model O modelo LLM com configurações específicas
     * @param array $options Opções adicionais para sobrescrever configurações do modelo
     * @return string A resposta do modelo de IA
     */
    public function sendConversationWithModel(array $messages, LlmModel $model, array $options = []): string;

    /**
     * Envia uma mensagem com anexos usando configurações de um LLMModel específico.
     *
     * @param string $prompt O prompt ou mensagem a ser enviada
     * @param LlmModel $model O modelo LLM com configurações específicas
     * @param array $attachments Array de anexos (Media objects ou caminhos)
     * @param array $options Opções adicionais para sobrescrever configurações do modelo
     * @return string A resposta do modelo de IA
     */
    public function sendMessageWithAttachmentsAndModel(string $prompt, LlmModel $model, array $attachments = [], array $options = []): string;

    /**
     * Gera embeddings para um texto.
     *
     * @param string $text O texto para gerar embeddings
     * @return array O vetor de embeddings
     */
    public function generateEmbeddings(string $text): array;

    /**
     * Retorna o nome do serviço de IA.
     *
     * @return string
     */
    public function getServiceName(): string;

    /**
     * Verifica se o serviço está configurado corretamente.
     *
     * @return bool
     */
    public function isConfigured(): bool;

    /**
     * Envia uma mensagem com anexos.
     *
     * @param string $prompt O prompt ou mensagem a ser enviada
     * @param array $options Opções adicionais para a requisição
     * @param array $attachments Array de anexos (Media objects ou caminhos)
     * @return string A resposta do modelo de IA
     */
    public function sendMessageWithAttachments(string $prompt, array $options = [], array $attachments = []): string;
}
