<?php

namespace App\Services\AI;

class AIService
{
    /**
     * Instância do serviço de IA.
     *
     * @var AIServiceInterface
     */
    protected AIServiceInterface $service;

    /**
     * Construtor.
     *
     * @param AIServiceInterface|null $service
     */
    public function __construct(?AIServiceInterface $service = null)
    {
        $this->service = $service ?? AIServiceFactory::createDefault();
    }

    /**
     * Define o serviço de IA a ser usado.
     *
     * @param AIServiceInterface $service
     * @return self
     */
    public function setService(AIServiceInterface $service): self
    {
        $this->service = $service;
        return $this;
    }

    /**
     * Obtém o serviço de IA atual.
     *
     * @return AIServiceInterface
     */
    public function getService(): AIServiceInterface
    {
        return $this->service;
    }

    /**
     * Cria uma nova instância com um serviço específico.
     *
     * @param string $type
     * @param array $config
     * @return self
     */
    public function withService(string $type, array $config = []): self
    {
        // Garantir que as configurações do arquivo config/ai.php sejam usadas
        $aiConfig = config('ai');
        $serviceType = strtolower($type);

        // Obter configurações específicas do serviço
        $serviceConfig = isset($aiConfig[$serviceType]) ? $aiConfig[$serviceType] : [];

        // Mesclar com as configurações fornecidas
        $mergedConfig = array_merge($serviceConfig, $config);

        $service = AIServiceFactory::create($type, $mergedConfig);
        return new self($service);
    }

    /**
     * Envia uma mensagem para o modelo de IA.
     *
     * @param string $prompt
     * @param array $options
     * @return string
     */
    public function sendMessage(string $prompt, array $options = []): string
    {
        return $this->service->sendMessage($prompt, $options);
    }

    /**
     * Envia uma conversa para o modelo de IA.
     *
     * @param array $messages
     * @param array $options
     * @return string
     */
    public function sendConversation(array $messages, array $options = []): string
    {
        return $this->service->sendConversation($messages, $options);
    }

    /**
     * Gera embeddings para um texto.
     *
     * @param string $text
     * @return array
     */
    public function generateEmbeddings(string $text): array
    {
        return $this->service->generateEmbeddings($text);
    }

    /**
     * Envia uma mensagem usando configurações de um LLMModel específico.
     *
     * @param string $prompt
     * @param \App\Models\LlmModel $model
     * @param array $options
     * @return string
     */
    public function sendMessageWithModel(string $prompt, \App\Models\LlmModel $model, array $options = []): string
    {
        return $this->service->sendMessageWithModel($prompt, $model, $options);
    }

    /**
     * Envia uma conversa usando configurações de um LLMModel específico.
     *
     * @param array $messages
     * @param \App\Models\LlmModel $model
     * @param array $options
     * @return string
     */
    public function sendConversationWithModel(array $messages, \App\Models\LlmModel $model, array $options = []): string
    {
        return $this->service->sendConversationWithModel($messages, $model, $options);
    }

    /**
     * Envia uma mensagem com anexos usando configurações de um LLMModel específico.
     *
     * @param string $prompt
     * @param \App\Models\LlmModel $model
     * @param array $attachments
     * @param array $options
     * @return string
     */
    public function sendMessageWithAttachmentsAndModel(string $prompt, \App\Models\LlmModel $model, array $attachments = [], array $options = []): string
    {
        return $this->service->sendMessageWithAttachmentsAndModel($prompt, $model, $attachments, $options);
    }

    /**
     * Envia uma mensagem com anexos para o modelo de IA.
     *
     * @param string $prompt
     * @param array $options
     * @param array $attachments Array de anexos (Media objects, caminhos de arquivos ou UploadedFile)
     * @return string
     */
    public function sendMessageWithAttachments(string $prompt, array $options = [], array $attachments = []): string
    {
        return $this->service->sendMessageWithAttachments($prompt, $options, $attachments);
    }

    /**
     * Retorna o nome do serviço de IA.
     *
     * @return string
     */
    public function getServiceName(): string
    {
        return $this->service->getServiceName();
    }

    /**
     * Verifica se o serviço está configurado corretamente.
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return $this->service->isConfigured();
    }

    /**
     * Cria uma nova instância do AIService configurada para um LLMModel específico.
     *
     * @param \App\Models\LlmModel $model
     * @param string|null $serviceType
     * @param array $additionalConfig
     * @return self
     */
    public static function forModel(\App\Models\LlmModel $model, ?string $serviceType = null, array $additionalConfig = []): self
    {
        $service = AIServiceFactory::createForModel($model, $serviceType, $additionalConfig);
        return new self($service);
    }

    /**
     * Cria uma nova instância do AIService usando Ollama configurada para um LLMModel específico.
     *
     * @param \App\Models\LlmModel $model
     * @param array $additionalConfig
     * @return self
     */
    public static function forModelWithOllama(\App\Models\LlmModel $model, array $additionalConfig = []): self
    {
        $service = AIServiceFactory::createOllamaForModel($model, $additionalConfig);
        return new self($service);
    }
}
