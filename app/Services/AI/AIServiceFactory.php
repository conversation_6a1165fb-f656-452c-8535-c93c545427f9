<?php

namespace App\Services\AI;

use App\Models\LlmModel;
use InvalidArgumentException;

class AIServiceFactory
{
    /**
     * Cria uma instância do serviço de IA com base no tipo especificado.
     *
     * @param string $type Tipo de serviço ('openai', 'google', 'openrouter')
     * @param array $config Configurações adicionais
     * @return AIServiceInterface
     * @throws InvalidArgumentException
     */
    public static function create(string $type, array $config = []): AIServiceInterface
    {
        // Garantir que as configurações do arquivo config/ai.php sejam usadas
        $aiConfig = config('ai');

        // Mesclar configurações específicas do serviço com as configurações fornecidas
        $serviceType = strtolower($type);
        $serviceConfig = isset($aiConfig[$serviceType]) ? $aiConfig[$serviceType] : [];
        $mergedConfig = array_merge($serviceConfig, $config);

        return match ($serviceType) {
            'ollama' => new OllamaService($mergedConfig),
            default => throw new InvalidArgumentException("Unsupported AI service type: {$type}"),
        };
    }

    /**
     * Cria uma instância do serviço de IA com base na configuração.
     *
     * @param array $config Configurações
     * @return AIServiceInterface
     */
    public static function createFromConfig(array $config = []): AIServiceInterface
    {
        $type = $config['type'] ?? env('AI_SERVICE_TYPE', 'openai');
        return self::create($type, $config);
    }

    /**
     * Cria uma instância do serviço de IA padrão com base na configuração do ambiente.
     *
     * @return AIServiceInterface
     */
    public static function createDefault(): AIServiceInterface
    {
        $type = env('AI_SERVICE_TYPE', 'openai');
        return self::create($type);
    }

    /**
     * Cria uma instância do serviço de IA configurada para um LLMModel específico.
     *
     * @param LlmModel $model O modelo LLM a ser usado
     * @param string|null $serviceType Tipo de serviço (se null, usa o padrão do ambiente)
     * @param array $additionalConfig Configurações adicionais
     * @return AIServiceInterface
     */
    public static function createForModel(LlmModel $model, ?string $serviceType = null, array $additionalConfig = []): AIServiceInterface
    {
        $type = $serviceType ?? env('AI_SERVICE_TYPE', 'ollama');

        // Obter configurações base do serviço
        $aiConfig = config('ai');
        $serviceConfig = isset($aiConfig[strtolower($type)]) ? $aiConfig[strtolower($type)] : [];

        // Mesclar com configurações adicionais
        $config = array_merge($serviceConfig, $additionalConfig);

        // Sobrescrever o modelo com o nome do LLMModel
        $config['model'] = $model->name;

        // Criar o serviço
        $service = self::create($type, $config);

        // Se for um serviço que suporta LLMModel, configurar as opções do modelo
        if (method_exists($service, 'setLlmModel')) {
            $service->setLlmModel($model);
        }

        return $service;
    }

    /**
     * Cria uma instância do serviço Ollama configurada para um LLMModel específico.
     *
     * @param LlmModel $model O modelo LLM a ser usado
     * @param array $additionalConfig Configurações adicionais
     * @return OllamaService
     */
    public static function createOllamaForModel(LlmModel $model, array $additionalConfig = []): OllamaService
    {
        $service = self::createForModel($model, 'ollama', $additionalConfig);

        if (!$service instanceof OllamaService) {
            throw new InvalidArgumentException('Failed to create OllamaService instance');
        }

        return $service;
    }
}
