<?php

namespace App\Services;

use App\Models\LlmModel;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class ModelMetadataService
{
    private const HUGGINGFACE_API_BASE = 'https://huggingface.co/api';
    private const RATE_LIMIT_DELAY = 1; // seconds between requests
    private const CACHE_TTL = 3600; // 1 hour cache for API responses

    /**
     * Fetch model metadata from Hugging Face
     */
    public function fetchModelMetadata(string $huggingfaceId): ?array
    {
        try {
            $cacheKey = "hf_metadata_{$huggingfaceId}";
            
            // Check cache first
            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            // Rate limiting
            $this->respectRateLimit();

            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->get("{$this->getApiUrl()}/models/{$huggingfaceId}");

            if (!$response->successful()) {
                Log::warning("Failed to fetch HF metadata for {$huggingfaceId}", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return null;
            }

            $data = $response->json();
            
            // Cache the response
            Cache::put($cacheKey, $data, self::CACHE_TTL);
            
            return $data;

        } catch (Exception $e) {
            Log::error("Error fetching HF metadata for {$huggingfaceId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Parse model metadata and extract relevant information
     */
    public function parseModelMetadata(array $metadata, string $modelName): array
    {
        $parsed = [
            'display_name' => $metadata['id'] ?? null,
            'description' => $this->extractDescription($metadata),
            'huggingface_url' => "https://huggingface.co/{$metadata['id']}",
            'tags' => $metadata['tags'] ?? [],
            'license' => $this->extractLicense($metadata),
            'model_size_bytes' => $this->extractModelSize($metadata),
            'huggingface_updated_at' => $this->parseDate($metadata['lastModified'] ?? null),
        ];

        // Extract capabilities from tags and model card
        $capabilities = $this->extractCapabilities($metadata, $modelName);
        $parsed = array_merge($parsed, $capabilities);

        // Extract technical specifications
        $specs = $this->extractTechnicalSpecs($metadata, $modelName);
        $parsed = array_merge($parsed, $specs);

        return $parsed;
    }

    /**
     * Extract model capabilities from metadata
     */
    private function extractCapabilities(array $metadata, string $modelName): array
    {
        $tags = array_map('strtolower', $metadata['tags'] ?? []);
        $modelNameLower = strtolower($modelName);
        $description = strtolower($metadata['description'] ?? '');
        $cardData = $metadata['cardData'] ?? [];

        return [
            'has_reasoning' => $this->detectReasoning($tags, $modelNameLower, $description, $cardData),
            'has_tool_usage' => $this->detectToolUsage($tags, $modelNameLower, $description, $cardData),
            'has_vision' => $this->detectVision($tags, $modelNameLower, $description, $cardData),
            'has_code_generation' => $this->detectCodeGeneration($tags, $modelNameLower, $description, $cardData),
            'has_function_calling' => $this->detectFunctionCalling($tags, $modelNameLower, $description, $cardData),
        ];
    }

    /**
     * Extract technical specifications
     */
    private function extractTechnicalSpecs(array $metadata, string $modelName): array
    {
        $specs = [
            'architecture' => $this->extractArchitecture($metadata, $modelName),
            'parameter_count' => $this->extractParameterCount($metadata, $modelName),
            'quantization' => $this->extractQuantization($modelName),
            'format' => $this->extractFormat($metadata, $modelName),
            'context_window' => $this->extractContextWindow($metadata),
        ];

        // Parse numeric parameter count
        if ($specs['parameter_count']) {
            $specs['parameter_count_numeric'] = LlmModel::parseParameterCount($specs['parameter_count']);
        }

        return $specs;
    }

    /**
     * Detect reasoning capabilities
     */
    private function detectReasoning(array $tags, string $modelName, string $description, array $cardData): bool
    {
        $reasoningKeywords = ['reasoning', 'thinking', 'chain-of-thought', 'cot', 'step-by-step', 'logic'];
        
        foreach ($reasoningKeywords as $keyword) {
            if (in_array($keyword, $tags) || 
                str_contains($modelName, $keyword) || 
                str_contains($description, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect tool usage capabilities
     */
    private function detectToolUsage(array $tags, string $modelName, string $description, array $cardData): bool
    {
        $toolKeywords = ['tool-use', 'tools', 'function-calling', 'agent', 'api'];
        
        foreach ($toolKeywords as $keyword) {
            if (in_array($keyword, $tags) || 
                str_contains($modelName, $keyword) || 
                str_contains($description, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect vision capabilities
     */
    private function detectVision(array $tags, string $modelName, string $description, array $cardData): bool
    {
        $visionKeywords = ['vision', 'multimodal', 'image', 'visual', 'vlm'];
        
        foreach ($visionKeywords as $keyword) {
            if (in_array($keyword, $tags) || 
                str_contains($modelName, $keyword) || 
                str_contains($description, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect code generation capabilities
     */
    private function detectCodeGeneration(array $tags, string $modelName, string $description, array $cardData): bool
    {
        $codeKeywords = ['code', 'coding', 'programming', 'coder', 'codegen'];
        
        foreach ($codeKeywords as $keyword) {
            if (in_array($keyword, $tags) || 
                str_contains($modelName, $keyword) || 
                str_contains($description, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect function calling capabilities
     */
    private function detectFunctionCalling(array $tags, string $modelName, string $description, array $cardData): bool
    {
        $functionKeywords = ['function-calling', 'function', 'tool-calling', 'structured-output'];
        
        foreach ($functionKeywords as $keyword) {
            if (in_array($keyword, $tags) || 
                str_contains($modelName, $keyword) || 
                str_contains($description, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract model architecture
     */
    private function extractArchitecture(array $metadata, string $modelName): ?string
    {
        $architectures = ['llama', 'qwen', 'mistral', 'gemma', 'phi', 'codellama', 'deepseek'];
        $modelNameLower = strtolower($modelName);
        
        foreach ($architectures as $arch) {
            if (str_contains($modelNameLower, $arch)) {
                return ucfirst($arch);
            }
        }

        // Check in tags
        $tags = array_map('strtolower', $metadata['tags'] ?? []);
        foreach ($architectures as $arch) {
            if (in_array($arch, $tags)) {
                return ucfirst($arch);
            }
        }

        return null;
    }

    /**
     * Extract parameter count
     */
    private function extractParameterCount(array $metadata, string $modelName): ?string
    {
        // Try to extract from model name
        if (preg_match('/(\d+(?:\.\d+)?[KMBT]?)(?:-|_|\s)/i', $modelName, $matches)) {
            return strtoupper($matches[1]);
        }

        // Try to extract from tags
        $tags = $metadata['tags'] ?? [];
        foreach ($tags as $tag) {
            if (preg_match('/(\d+(?:\.\d+)?[KMBT]?)$/i', $tag, $matches)) {
                return strtoupper($matches[1]);
            }
        }

        return null;
    }

    /**
     * Extract quantization type
     */
    private function extractQuantization(string $modelName): ?string
    {
        if (preg_match('/(Q\d+_[A-Z0-9]+|FP16|FP32|INT8|INT4)/i', $modelName, $matches)) {
            return strtoupper($matches[1]);
        }

        return null;
    }

    /**
     * Extract model format
     */
    private function extractFormat(array $metadata, string $modelName): ?string
    {
        if (str_contains(strtolower($modelName), 'gguf')) {
            return 'GGUF';
        }

        $tags = array_map('strtolower', $metadata['tags'] ?? []);
        if (in_array('gguf', $tags)) {
            return 'GGUF';
        }
        if (in_array('safetensors', $tags)) {
            return 'SafeTensors';
        }

        return null;
    }

    /**
     * Extract context window size
     */
    private function extractContextWindow(array $metadata): ?int
    {
        $cardData = $metadata['cardData'] ?? [];
        
        // Common context window sizes to look for
        $contextSizes = [4096, 8192, 16384, 32768, 65536, 131072, 262144];
        
        $description = strtolower($metadata['description'] ?? '');
        foreach ($contextSizes as $size) {
            if (str_contains($description, (string)$size)) {
                return $size;
            }
        }

        return null;
    }

    /**
     * Extract description from metadata
     */
    private function extractDescription(array $metadata): ?string
    {
        return $metadata['description'] ?? null;
    }

    /**
     * Extract license information
     */
    private function extractLicense(array $metadata): ?string
    {
        $cardData = $metadata['cardData'] ?? [];
        return $cardData['license'] ?? null;
    }

    /**
     * Extract model size in bytes
     */
    private function extractModelSize(array $metadata): ?int
    {
        $siblings = $metadata['siblings'] ?? [];
        $totalSize = 0;
        
        foreach ($siblings as $sibling) {
            if (isset($sibling['size'])) {
                $totalSize += $sibling['size'];
            }
        }
        
        return $totalSize > 0 ? $totalSize : null;
    }

    /**
     * Parse date string to timestamp
     */
    private function parseDate(?string $dateString): ?string
    {
        if (!$dateString) {
            return null;
        }

        try {
            return \Carbon\Carbon::parse($dateString)->toDateTimeString();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get API URL with fallback
     */
    private function getApiUrl(): string
    {
        return self::HUGGINGFACE_API_BASE;
    }

    /**
     * Respect rate limiting
     */
    private function respectRateLimit(): void
    {
        $lastRequest = Cache::get('hf_last_request', 0);
        $timeSinceLastRequest = time() - $lastRequest;
        
        if ($timeSinceLastRequest < self::RATE_LIMIT_DELAY) {
            sleep(self::RATE_LIMIT_DELAY - $timeSinceLastRequest);
        }
        
        Cache::put('hf_last_request', time(), 60);
    }
}
