<?php

namespace App\Jobs;

use App\Models\LlmModel;
use App\Services\ModelMetadataService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class CrawlModelMetadataJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $tries = 3;
    public $timeout = 300; // 5 minutes
    public $retry_after = 1800; // 30 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public string $modelName,
        public bool $forceUpdate = false
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(ModelMetadataService $metadataService): void
    {
        Log::info("Starting metadata crawl for model: {$this->modelName}");

        try {
            // Find or create the model
            $model = LlmModel::firstOrNew(['name' => $this->modelName]);
            
            // Check if we need to update
            if (!$this->forceUpdate && $model->exists && !$model->needsMetadataUpdate()) {
                Log::info("Model {$this->modelName} metadata is up to date, skipping");
                return;
            }

            // Extract Hugging Face ID from model name
            $huggingfaceId = LlmModel::extractHuggingFaceId($this->modelName);
            
            if (!$huggingfaceId) {
                Log::warning("Could not extract Hugging Face ID from model name: {$this->modelName}");
                $this->markCrawlFailed($model, 'Could not extract Hugging Face ID');
                return;
            }

            // Fetch metadata from Hugging Face
            $rawMetadata = $metadataService->fetchModelMetadata($huggingfaceId);
            
            if (!$rawMetadata) {
                Log::warning("Failed to fetch metadata for {$huggingfaceId}");
                $this->markCrawlFailed($model, 'Failed to fetch metadata from Hugging Face');
                return;
            }

            // Parse the metadata
            $parsedData = $metadataService->parseModelMetadata($rawMetadata, $this->modelName);

            // Update model with parsed data
            $model->fill([
                'huggingface_id' => $huggingfaceId,
                'huggingface_metadata' => $rawMetadata,
                'last_crawled_at' => now(),
                'crawl_metadata' => [
                    'status' => 'success',
                    'crawled_at' => now()->toISOString(),
                    'job_id' => $this->job->getJobId(),
                ],
            ]);

            // Merge parsed data
            $model->fill($parsedData);

            // Set default display name if not provided
            if (!$model->display_name) {
                $model->display_name = $this->generateDisplayName($this->modelName);
            }

            $model->save();

            Log::info("Successfully updated metadata for model: {$this->modelName}", [
                'huggingface_id' => $huggingfaceId,
                'capabilities' => $model->capabilities,
                'architecture' => $model->architecture,
                'parameter_count' => $model->parameter_count,
            ]);

        } catch (Exception $e) {
            Log::error("Error crawling metadata for {$this->modelName}: " . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
            ]);

            $this->fail($e);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("CrawlModelMetadataJob failed definitively for model {$this->modelName}: " . $exception->getMessage());

        try {
            $model = LlmModel::firstOrNew(['name' => $this->modelName]);
            $this->markCrawlFailed($model, $exception->getMessage());
        } catch (Exception $e) {
            Log::error("Failed to mark crawl as failed for {$this->modelName}: " . $e->getMessage());
        }
    }

    /**
     * Mark the crawl as failed
     */
    private function markCrawlFailed(LlmModel $model, string $error): void
    {
        $model->fill([
            'last_crawled_at' => now(),
            'crawl_metadata' => [
                'status' => 'failed',
                'error' => $error,
                'failed_at' => now()->toISOString(),
                'job_id' => $this->job?->getJobId(),
                'attempts' => $this->attempts(),
            ],
        ]);

        // Set default display name if this is a new model
        if (!$model->exists && !$model->display_name) {
            $model->display_name = $this->generateDisplayName($this->modelName);
        }

        $model->save();
    }

    /**
     * Generate a display name from model name
     */
    private function generateDisplayName(string $modelName): string
    {
        // Remove "hf.co/" prefix if present
        $name = str_replace('hf.co/', '', $modelName);
        
        // Split by colon to separate model from quantization
        $parts = explode(':', $name);
        $baseName = $parts[0];
        $quantization = $parts[1] ?? null;
        
        // Extract the model name part (after the last slash)
        $nameParts = explode('/', $baseName);
        $modelPart = end($nameParts);
        
        // Clean up the name
        $displayName = str_replace(['-', '_'], ' ', $modelPart);
        $displayName = ucwords($displayName);
        
        // Add quantization if present
        if ($quantization) {
            $displayName .= " ({$quantization})";
        }
        
        return $displayName;
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['model-metadata', 'crawl', $this->modelName];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [60, 300, 900]; // 1 minute, 5 minutes, 15 minutes
    }

    /**
     * Determine if the job should be retried based on the exception.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(6); // Stop retrying after 6 hours
    }
}
