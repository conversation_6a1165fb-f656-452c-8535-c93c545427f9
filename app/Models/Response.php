<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Response extends Model
{
    protected $fillable = [
        'uuid',
        'prompt_id',
        'model',
        'content',
        'reasoning',
        'metadata',
        'status',
        'generated_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'generated_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($response) {
            if (empty($response->uuid)) {
                $response->uuid = (string) Str::uuid();
            }
        });
    }

    public function prompt(): BelongsTo
    {
        return $this->belongsTo(Prompt::class);
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     * Check if this response has reasoning content
     */
    public function hasReasoning(): bool
    {
        return !empty($this->reasoning);
    }

    /**
     * Get the full content including reasoning (for backward compatibility)
     */
    public function getFullContentAttribute(): string
    {
        if ($this->hasReasoning()) {
            return "<think>\n{$this->reasoning}\n</think>\n\n{$this->content}";
        }

        return $this->content;
    }
}
