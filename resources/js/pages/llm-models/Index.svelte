<script lang="ts">
    import { onMount } from 'svelte';
    import { Link } from '@inertiajs/svelte';
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Input } from '@/components/ui/input';
    import { Badge } from '@/components/ui/badge';
    import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
    import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';
    import { Separator } from '@/components/ui/separator';
    import { api } from '@/lib/api';
    import type { LlmModel, LlmModelFilters, PaginatedResponse, BreadcrumbItem } from '@/types';
    import { Plus, Search, RefreshCw, Filter, Eye, Edit, Trash2 } from 'lucide-svelte';

    const breadcrumbItems: BreadcrumbItem[] = [
        {
            title: 'LLM Models',
            href: '/llm-models',
        },
    ];

    let models: LlmModel[] = [];
    let loading = true;
    let error = '';
    let pagination = {
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0,
        from: 0,
        to: 0,
    };

    let filters: LlmModelFilters = {
        search: '',
        status: undefined,
        architecture: undefined,
        capability: undefined,
        sort_by: 'created_at',
        sort_order: 'desc',
    };

    let filterOptions = {
        architectures: [] as string[],
        capabilities: {} as Record<string, string>,
        statuses: ['active', 'inactive', 'deprecated'],
    };

    onMount(async () => {
        await loadFilterOptions();
        await loadModels();
    });

    async function loadFilterOptions() {
        try {
            filterOptions = await api.getLlmModelFilterOptions();
        } catch (err) {
            console.error('Failed to load filter options:', err);
            // Set default filter options to prevent errors
            filterOptions = {
                architectures: [],
                capabilities: {
                    reasoning: 'Reasoning',
                    tool_usage: 'Tool Usage',
                    vision: 'Vision',
                    code_generation: 'Code Generation',
                    function_calling: 'Function Calling',
                },
                statuses: ['active', 'inactive', 'deprecated'],
                parameter_ranges: []
            };
        }
    }

    async function loadModels() {
        loading = true;
        error = '';

        try {
            const response = await api.getLlmModels(filters);
            models = response.data || [];
            pagination = {
                current_page: response.current_page || 1,
                last_page: response.last_page || 1,
                per_page: response.per_page || 15,
                total: response.total || 0,
                from: response.from || 0,
                to: response.to || 0
            };
        } catch (err) {
            error = 'Failed to load models';
            console.error('Error loading models:', err);
            models = [];
            pagination = {
                current_page: 1,
                last_page: 1,
                per_page: 15,
                total: 0,
                from: 0,
                to: 0
            };
        } finally {
            loading = false;
        }
    }

    async function handleSearch() {
        pagination.current_page = 1;
        await loadModels();
    }

    async function handleFilterChange() {
        pagination.current_page = 1;
        await loadModels();
    }

    async function refreshMetadata(model: LlmModel) {
        try {
            await api.refreshLlmModelMetadata(model.uuid);
            // Show success message or toast
            await loadModels(); // Reload to show updated status
        } catch (err) {
            console.error('Failed to refresh metadata:', err);
        }
    }

    async function deleteModel(model: LlmModel) {
        if (!confirm(`Are you sure you want to delete "${model.display_name || model.name}"?`)) {
            return;
        }

        try {
            await api.deleteLlmModel(model.uuid);
            await loadModels(); // Reload the list
        } catch (err) {
            console.error('Failed to delete model:', err);
        }
    }

    function getCapabilityBadges(model: LlmModel) {
        return model.capabilities_list.map(cap => ({
            name: cap,
            label: filterOptions.capabilities[cap] || cap,
        }));
    }

    function getStatusColor(status: string) {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'inactive': return 'bg-yellow-100 text-yellow-800';
            case 'deprecated': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }
</script>

<svelte:head>
    <title>LLM Models</title>
</svelte:head>

<AppLayout {breadcrumbItems}>
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold tracking-tight">LLM Models</h1>
                <p class="text-muted-foreground">
                    Manage and monitor your language model metadata
                </p>
            </div>
            <Link href="/llm-models/create">
                <Button>
                    <Plus class="mr-2 h-4 w-4" />
                    Add Model
                </Button>
            </Link>
        </div>

        <!-- Filters -->
        <Card>
            <CardHeader>
                <CardTitle class="flex items-center gap-2">
                    <Filter class="h-4 w-4" />
                    Filters
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="space-y-2">
                        <label for="search-input" class="text-sm font-medium">Search</label>
                        <div class="relative">
                            <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                id="search-input"
                                bind:value={filters.search}
                                placeholder="Search models..."
                                class="pl-8"
                                on:keydown={(e) => e.key === 'Enter' && handleSearch()}
                            />
                        </div>
                    </div>

                    <div class="space-y-2">
                        <span class="text-sm font-medium">Status</span>
                        <Select bind:value={filters.status} onValueChange={handleFilterChange}>
                            <SelectTrigger>
                                {filters.status || "All statuses"}
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={undefined}>All statuses</SelectItem>
                                {#each filterOptions.statuses as status}
                                    <SelectItem value={status}>{status}</SelectItem>
                                {/each}
                            </SelectContent>
                        </Select>
                    </div>

                    <div class="space-y-2">
                        <span class="text-sm font-medium">Architecture</span>
                        <Select bind:value={filters.architecture} onValueChange={handleFilterChange}>
                            <SelectTrigger>
                                {filters.architecture || "All architectures"}
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={undefined}>All architectures</SelectItem>
                                {#each filterOptions.architectures as arch}
                                    <SelectItem value={arch}>{arch}</SelectItem>
                                {/each}
                            </SelectContent>
                        </Select>
                    </div>

                    <div class="space-y-2">
                        <span class="text-sm font-medium">Capability</span>
                        <Select bind:value={filters.capability} onValueChange={handleFilterChange}>
                            <SelectTrigger>
                                {(filters.capability && filterOptions.capabilities[filters.capability]) || "All capabilities"}
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={undefined}>All capabilities</SelectItem>
                                {#each Object.entries(filterOptions.capabilities) as [key, label]}
                                    <SelectItem value={key}>{label}</SelectItem>
                                {/each}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div class="flex gap-2 mt-4">
                    <Button on:click={handleSearch} variant="outline">
                        <Search class="mr-2 h-4 w-4" />
                        Search
                    </Button>
                    <Button 
                        on:click={() => {
                            filters = { sort_by: 'created_at', sort_order: 'desc' };
                            handleFilterChange();
                        }} 
                        variant="outline"
                    >
                        Clear Filters
                    </Button>
                </div>
            </CardContent>
        </Card>

        <!-- Models List -->
        {#if loading}
            <div class="flex items-center justify-center py-8">
                <RefreshCw class="h-6 w-6 animate-spin" />
                <span class="ml-2">Loading models...</span>
            </div>
        {:else if error}
            <Card>
                <CardContent class="py-8">
                    <div class="text-center text-red-600">
                        <p>{error}</p>
                        <Button on:click={loadModels} variant="outline" class="mt-4">
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        {:else if models.length === 0}
            <Card>
                <CardContent class="py-8">
                    <div class="text-center text-muted-foreground">
                        <p>No models found</p>
                        <Link href="/llm-models/create">
                            <Button class="mt-4">
                                <Plus class="mr-2 h-4 w-4" />
                                Add Your First Model
                            </Button>
                        </Link>
                    </div>
                </CardContent>
            </Card>
        {:else}
            <div class="grid gap-4">
                {#each models as model}
                    <Card>
                        <CardContent class="p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h3 class="text-lg font-semibold">
                                            {model.display_name || model.name}
                                        </h3>
                                        <Badge class={getStatusColor(model.status)}>
                                            {model.status}
                                        </Badge>
                                        {#if model.metadata.needs_update}
                                            <Badge variant="outline" class="text-orange-600 border-orange-600">
                                                Needs Update
                                            </Badge>
                                        {/if}
                                    </div>

                                    <p class="text-sm text-muted-foreground mb-3">
                                        {model.name}
                                    </p>

                                    {#if model.description}
                                        <p class="text-sm mb-3 line-clamp-2">
                                            {model.description}
                                        </p>
                                    {/if}

                                    <div class="flex flex-wrap gap-2 mb-3">
                                        {#if model.specifications.architecture}
                                            <Badge variant="secondary">
                                                {model.specifications.architecture}
                                            </Badge>
                                        {/if}
                                        {#if model.specifications.parameter_count}
                                            <Badge variant="secondary">
                                                {model.specifications.parameter_count}
                                            </Badge>
                                        {/if}
                                        {#if model.specifications.quantization}
                                            <Badge variant="secondary">
                                                {model.specifications.quantization}
                                            </Badge>
                                        {/if}
                                    </div>

                                    <div class="flex flex-wrap gap-1">
                                        {#each getCapabilityBadges(model) as capability}
                                            <Badge variant="outline" class="text-xs">
                                                {capability.label}
                                            </Badge>
                                        {/each}
                                    </div>
                                </div>

                                <div class="flex items-center gap-2 ml-4">
                                    {#if model.huggingface_id}
                                        <Button
                                            on:click={() => refreshMetadata(model)}
                                            variant="outline"
                                            size="sm"
                                        >
                                            <RefreshCw class="h-4 w-4" />
                                        </Button>
                                    {/if}
                                    
                                    <Link href={`/llm-models/${model.uuid}`}>
                                        <Button variant="outline" size="sm">
                                            <Eye class="h-4 w-4" />
                                        </Button>
                                    </Link>
                                    
                                    <Link href={`/llm-models/${model.uuid}/edit`}>
                                        <Button variant="outline" size="sm">
                                            <Edit class="h-4 w-4" />
                                        </Button>
                                    </Link>
                                    
                                    <Button
                                        on:click={() => deleteModel(model)}
                                        variant="outline"
                                        size="sm"
                                        class="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                {/each}
            </div>

            <!-- Pagination -->
            {#if pagination.last_page > 1}
                <div class="flex items-center justify-between">
                    <p class="text-sm text-muted-foreground">
                        Showing {pagination.from} to {pagination.to} of {pagination.total} models
                    </p>
                    <div class="flex gap-2">
                        <Button
                            variant="outline"
                            disabled={pagination.current_page === 1}
                            on:click={() => {
                                pagination.current_page--;
                                loadModels();
                            }}
                        >
                            Previous
                        </Button>
                        <Button
                            variant="outline"
                            disabled={pagination.current_page === pagination.last_page}
                            on:click={() => {
                                pagination.current_page++;
                                loadModels();
                            }}
                        >
                            Next
                        </Button>
                    </div>
                </div>
            {/if}
        {/if}
    </div>
</AppLayout>
