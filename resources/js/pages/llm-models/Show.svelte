<script lang="ts">
    import { onMount } from 'svelte';
    import { Link, router } from '@inertiajs/svelte';
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Badge } from '@/components/ui/badge';
    import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
    import { api } from '@/lib/api';
    import type { LlmModel, BreadcrumbItem } from '@/types';
    import {
        ArrowLeft,
        Edit,
        RefreshCw,
        ExternalLink,
        Database,
        Cpu,
        Zap,
        Shield,
        Tag,
        Activity,
        MessageSquare,
        Copy,
        Eye,
        EyeOff
    } from 'lucide-svelte';

    export let uuid: string;

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'LLM Models',
            href: '/llm-models',
        },
        {
            title: 'Model Details',
            href: `/llm-models/${uuid}`,
        },
    ];

    let model: LlmModel | null = null;
    let loading = true;
    let error = '';
    let refreshing = false;
    let showFullTemplate = false;
    let copySuccess = false;

    onMount(async () => {
        await loadModel();
    });

    async function loadModel() {
        loading = true;
        error = '';
        
        try {
            model = await api.getLlmModel(uuid);
        } catch (err) {
            error = 'Failed to load model details';
            console.error('Error loading model:', err);
        } finally {
            loading = false;
        }
    }

    async function refreshMetadata() {
        if (!model?.huggingface_id) return;
        
        refreshing = true;
        try {
            await api.refreshLlmModelMetadata(model.uuid);
            await loadModel(); // Reload to show updated data
        } catch (err) {
            console.error('Failed to refresh metadata:', err);
        } finally {
            refreshing = false;
        }
    }

    function getStatusColor(status: string) {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'inactive': return 'bg-yellow-100 text-yellow-800';
            case 'deprecated': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function formatDate(dateString?: string) {
        if (!dateString) return 'Never';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function toggleTemplateView() {
        showFullTemplate = !showFullTemplate;
    }

    async function copyChatTemplate() {
        if (!model?.formatting?.chat_template) return;

        try {
            await navigator.clipboard.writeText(model.formatting.chat_template);
            copySuccess = true;
            setTimeout(() => {
                copySuccess = false;
            }, 2000);
        } catch (err) {
            console.error('Failed to copy template:', err);
        }
    }

    function formatChatTemplate(template: string): string {
        if (!template) return '';

        // If template is too long and we're not showing full, truncate it
        if (!showFullTemplate && template.length > 500) {
            return template.substring(0, 500) + '...';
        }

        return template;
    }
</script>

<svelte:head>
    <title>{model?.display_name || model?.name || 'Model Details'}</title>
</svelte:head>

<AppLayout {breadcrumbs}>
    {#if loading}
        <div class="flex items-center justify-center py-8">
            <RefreshCw class="h-6 w-6 animate-spin" />
            <span class="ml-2">Loading model details...</span>
        </div>
    {:else if error}
        <Card>
            <CardContent class="py-8">
                <div class="text-center text-red-600">
                    <p>{error}</p>
                    <Button onclick={loadModel} variant="outline" class="mt-4">
                        Try Again
                    </Button>
                </div>
            </CardContent>
        </Card>
    {:else if model}
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center gap-3 mb-2">
                        <h1 class="text-3xl font-bold tracking-tight">
                            {model.display_name || model.name}
                        </h1>
                        <Badge class={getStatusColor(model.status)}>
                            {model.status}
                        </Badge>
                        {#if model.metadata.needs_update}
                            <Badge variant="outline" class="text-orange-600 border-orange-600">
                                Needs Update
                            </Badge>
                        {/if}
                    </div>
                    
                    <p class="text-muted-foreground mb-4">
                        {model.name}
                    </p>

                    {#if model.description}
                        <p class="text-sm mb-4 max-w-3xl">
                            {model.description}
                        </p>
                    {/if}
                </div>

                <div class="flex items-center gap-2">
                    <Button variant="outline" onclick={() => router.visit('/llm-models')}>
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back to Models
                    </Button>

                    {#if model.huggingface_id}
                        <Button
                            onclick={refreshMetadata}
                            variant="outline"
                            disabled={refreshing}
                        >
                            <RefreshCw class={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                            {refreshing ? 'Refreshing...' : 'Refresh Metadata'}
                        </Button>
                    {/if}
                    
                    <Link href={`/llm-models/${model.uuid}/edit`}>
                        <Button>
                            <Edit class="mr-2 h-4 w-4" />
                            Edit
                        </Button>
                    </Link>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Capabilities -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center gap-2">
                                <Zap class="h-5 w-5" />
                                Capabilities
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {#if model.capabilities_list.length > 0}
                                <div class="flex flex-wrap gap-2">
                                    {#each model.capabilities_list as capability}
                                        <Badge variant="secondary">
                                            {capability.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                        </Badge>
                                    {/each}
                                </div>
                            {:else}
                                <p class="text-muted-foreground">No specific capabilities defined</p>
                            {/if}
                        </CardContent>
                    </Card>

                    <!-- Technical Specifications -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center gap-2">
                                <Cpu class="h-5 w-5" />
                                Technical Specifications
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {#if model.specifications.architecture}
                                    <div>
                                        <dt class="text-sm font-medium text-muted-foreground">Architecture</dt>
                                        <dd class="text-sm">{model.specifications.architecture}</dd>
                                    </div>
                                {/if}

                                {#if model.specifications.parameter_count}
                                    <div>
                                        <dt class="text-sm font-medium text-muted-foreground">Parameters</dt>
                                        <dd class="text-sm">{model.specifications.parameter_count}</dd>
                                    </div>
                                {/if}

                                {#if model.specifications.quantization}
                                    <div>
                                        <dt class="text-sm font-medium text-muted-foreground">Quantization</dt>
                                        <dd class="text-sm">{model.specifications.quantization}</dd>
                                    </div>
                                {/if}

                                {#if model.specifications.format}
                                    <div>
                                        <dt class="text-sm font-medium text-muted-foreground">Format</dt>
                                        <dd class="text-sm">{model.specifications.format}</dd>
                                    </div>
                                {/if}

                                {#if model.specifications.context_window}
                                    <div>
                                        <dt class="text-sm font-medium text-muted-foreground">Context Window</dt>
                                        <dd class="text-sm">{model.specifications.context_window.toLocaleString()} tokens</dd>
                                    </div>
                                {/if}

                                {#if model.performance.estimated_vram_gb}
                                    <div>
                                        <dt class="text-sm font-medium text-muted-foreground">Estimated VRAM</dt>
                                        <dd class="text-sm">{model.performance.estimated_vram_gb} GB</dd>
                                    </div>
                                {/if}
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Chat Template -->
                    {#if model.formatting?.chat_template}
                        <Card>
                            <CardHeader>
                                <CardTitle class="flex items-center gap-2">
                                    <MessageSquare class="h-5 w-5" />
                                    Chat Template
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm text-muted-foreground">
                                            Template used for formatting conversations with this model
                                        </p>
                                        <div class="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onclick={toggleTemplateView}
                                            >
                                                {#if showFullTemplate}
                                                    <EyeOff class="mr-2 h-4 w-4" />
                                                    Show Less
                                                {:else}
                                                    <Eye class="mr-2 h-4 w-4" />
                                                    Show Full
                                                {/if}
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onclick={copyChatTemplate}
                                                disabled={copySuccess}
                                            >
                                                <Copy class="mr-2 h-4 w-4" />
                                                {copySuccess ? 'Copied!' : 'Copy'}
                                            </Button>
                                        </div>
                                    </div>

                                    <div class="relative">
                                        <pre class="bg-muted p-4 rounded-lg text-sm overflow-x-auto max-h-96 overflow-y-auto border"><code>{formatChatTemplate(model.formatting.chat_template)}</code></pre>

                                        {#if !showFullTemplate && model.formatting.chat_template.length > 500}
                                            <div class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-muted to-transparent pointer-events-none rounded-b-lg"></div>
                                        {/if}
                                    </div>

                                    {#if model.formatting.chat_template.length > 500}
                                        <div class="text-xs text-muted-foreground text-center">
                                            Template length: {model.formatting.chat_template.length} characters
                                        </div>
                                    {/if}
                                </div>
                            </CardContent>
                        </Card>
                    {/if}

                    <!-- Performance -->
                    {#if model.performance.model_size_bytes || model.performance.benchmark_score}
                        <Card>
                            <CardHeader>
                                <CardTitle class="flex items-center gap-2">
                                    <Activity class="h-5 w-5" />
                                    Performance
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {#if model.performance.model_size_bytes}
                                        <div>
                                            <dt class="text-sm font-medium text-muted-foreground">Model Size</dt>
                                            <dd class="text-sm">{model.performance.model_size_formatted}</dd>
                                        </div>
                                    {/if}

                                    {#if model.performance.benchmark_score}
                                        <div>
                                            <dt class="text-sm font-medium text-muted-foreground">Benchmark Score</dt>
                                            <dd class="text-sm">{model.performance.benchmark_score}</dd>
                                        </div>
                                    {/if}
                                </div>
                            </CardContent>
                        </Card>
                    {/if}

                    <!-- Tags -->
                    {#if model.tags && model.tags.length > 0}
                        <Card>
                            <CardHeader>
                                <CardTitle class="flex items-center gap-2">
                                    <Tag class="h-5 w-5" />
                                    Tags
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="flex flex-wrap gap-2">
                                    {#each model.tags as tag}
                                        <Badge variant="outline">{tag}</Badge>
                                    {/each}
                                </div>
                            </CardContent>
                        </Card>
                    {/if}
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Info -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center gap-2">
                                <Database class="h-5 w-5" />
                                Quick Info
                            </CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-muted-foreground">Created</dt>
                                <dd class="text-sm">{formatDate(model.created_at)}</dd>
                            </div>

                            <div>
                                <dt class="text-sm font-medium text-muted-foreground">Last Updated</dt>
                                <dd class="text-sm">{formatDate(model.updated_at)}</dd>
                            </div>

                            <div>
                                <dt class="text-sm font-medium text-muted-foreground">Last Crawled</dt>
                                <dd class="text-sm">{formatDate(model.metadata.last_crawled_at)}</dd>
                            </div>

                            {#if model.responses_count !== undefined}
                                <div>
                                    <dt class="text-sm font-medium text-muted-foreground">Total Responses</dt>
                                    <dd class="text-sm">{model.responses_count}</dd>
                                </div>
                            {/if}
                        </CardContent>
                    </Card>

                    <!-- Licensing -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center gap-2">
                                <Shield class="h-5 w-5" />
                                Licensing
                            </CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-3">
                            {#if model.licensing.license}
                                <div>
                                    <dt class="text-sm font-medium text-muted-foreground">License</dt>
                                    <dd class="text-sm">{model.licensing.license}</dd>
                                </div>
                            {/if}

                            <div>
                                <dt class="text-sm font-medium text-muted-foreground">Commercial Use</dt>
                                <dd class="text-sm">
                                    <Badge variant={model.licensing.commercial_use ? "default" : "secondary"}>
                                        {model.licensing.commercial_use ? 'Allowed' : 'Not Allowed'}
                                    </Badge>
                                </dd>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- External Links -->
                    {#if model.huggingface_url}
                        <Card>
                            <CardHeader>
                                <CardTitle>External Links</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <a
                                    href={model.huggingface_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    class="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800"
                                >
                                    <ExternalLink class="h-4 w-4" />
                                    View on Hugging Face
                                </a>
                            </CardContent>
                        </Card>
                    {/if}
                </div>
            </div>
        </div>
    {/if}
</AppLayout>
