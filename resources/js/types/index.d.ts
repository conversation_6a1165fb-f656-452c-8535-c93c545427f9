import '@inertiajs/svelte';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: any;
    isActive?: boolean;
}

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    [key: string]: unknown;
    ziggy: Config & { location: string };
};

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;

// Prompt Management Types
export interface Prompt {
    id: number;
    uuid: string;
    content: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    metadata: Record<string, any>;
    user_id: number;
    processed_at: string | null;
    created_at: string;
    updated_at: string;
    responses?: Response[];
    attachments?: Attachment[];
    subjects?: Subject[];
}

export interface Response {
    id: number;
    uuid: string;
    prompt_id: number;
    model: string;
    content: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    metadata: {
        processing_time?: number;
        service_type?: string;
        has_attachments?: boolean;
        attachment_types?: string[];
        [key: string]: any;
    };
    generated_at: string | null;
    created_at: string;
    updated_at: string;
}

export interface Attachment {
    id: number;
    name: string;
    file_name: string;
    mime_type: string;
    size: number;
    url: string;
}

export interface Subject {
    id: number;
    name: string;
    description?: string;
    parent_id?: number;
    created_at: string;
    updated_at: string;
}

export interface LlmModel {
    id: number;
    uuid: string;
    name: string;
    display_name?: string;
    description?: string;

    // Hugging Face metadata
    huggingface_id?: string;
    huggingface_url?: string;
    huggingface_metadata?: Record<string, any>;

    // Capabilities
    capabilities: {
        reasoning: boolean;
        tool_usage: boolean;
        vision: boolean;
        code_generation: boolean;
        function_calling: boolean;
    };
    capabilities_list: string[];

    // Technical specifications
    specifications: {
        context_window?: number;
        parameter_count?: string;
        parameter_count_numeric?: number;
        architecture?: string;
        quantization?: string;
        format?: string;
    };

    // Chat and prompt formatting
    formatting: {
        chat_template?: string;
        prompt_templates?: Record<string, any>;
    };

    // Performance and resource requirements
    performance: {
        model_size_bytes?: number;
        model_size_formatted: string;
        estimated_vram_gb?: number;
        benchmark_score?: number;
        benchmark_details?: Record<string, any>;
    };

    // Licensing and usage
    licensing: {
        license?: string;
        commercial_use: boolean;
    };

    tags: string[];
    status: 'active' | 'inactive' | 'deprecated';

    // Metadata and timestamps
    metadata: {
        last_crawled_at?: string;
        huggingface_updated_at?: string;
        crawl_metadata?: Record<string, any>;
        needs_update: boolean;
    };

    created_at: string;
    updated_at: string;

    // Relationships
    responses_count?: number;
    recent_responses?: Array<{
        uuid: string;
        prompt_id: number;
        status: string;
        generated_at?: string;
        processing_time?: number;
    }>;
}

export interface LlmModelFilters {
    search?: string;
    status?: 'active' | 'inactive' | 'deprecated';
    architecture?: string;
    capability?: string;
    min_params?: number;
    max_params?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
}

export interface CreateLlmModelRequest {
    name: string;
    display_name?: string;
    description?: string;
    huggingface_id?: string;
    huggingface_url?: string;

    // Capabilities
    has_reasoning?: boolean;
    has_tool_usage?: boolean;
    has_vision?: boolean;
    has_code_generation?: boolean;
    has_function_calling?: boolean;

    // Technical specifications
    context_window?: number;
    parameter_count?: string;
    architecture?: string;
    quantization?: string;
    format?: string;

    // Chat and prompt formatting
    chat_template?: string;
    prompt_templates?: Record<string, any>;

    // Performance and resource requirements
    model_size_bytes?: number;
    estimated_vram_gb?: number;
    benchmark_score?: number;
    benchmark_details?: Record<string, any>;

    // Licensing and usage
    license?: string;
    commercial_use?: boolean;
    tags?: string[];

    status?: 'active' | 'inactive' | 'deprecated';
}

export interface LlmModelStatistics {
    total_models: number;
    active_models: number;
    models_with_metadata: number;
    models_needing_update: number;
    architectures: Record<string, number>;
    capabilities: {
        reasoning: number;
        tool_usage: number;
        vision: number;
        code_generation: number;
        function_calling: number;
    };
}

export interface LlmModelFilterOptions {
    architectures: string[];
    capabilities: Record<string, string>;
    statuses: string[];
    parameter_ranges: Array<{
        label: string;
        min: number;
        max?: number;
    }>;
}

export interface QueueJob {
    id: number;
    prompt_uuid: string | null;
    model: string | null;
    created_at: string;
    attempts: number;
    queue: string;
    user_id?: number;
    prompt_content?: string;
    estimated_completion?: string;
}

export interface CreatePromptRequest {
    content: string;
    models?: string[];
    attachments?: File[];
    metadata?: Record<string, any>;
}

export interface PromptFilters {
    search?: string;
    status?: string;
    model?: string;
    date_from?: string;
    date_to?: string;
}

export interface PaginatedResponse<T> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: Record<string, string[]>;
}

// Dashboard-specific interfaces
export interface OllamaModel {
    name: string;
    model: string;
    modified_at: string;
    size: number;
    digest: string;
    details: {
        parent_model: string;
        format: string;
        family: string;
        families: string[];
        parameter_size: string;
        quantization_level: string;
    };
}

export interface RunningModel {
    name: string;
    model: string;
    size: number;
    digest: string;
    details: {
        parent_model: string;
        format: string;
        family: string;
        families: string[];
        parameter_size: string;
        quantization_level: string;
    };
    expires_at: string;
    size_vram: number;
}

export interface OllamaData {
    available: boolean;
    url: string;
    models: Record<string, string>;
    running_models: RunningModel[];
    version: string | null;
}

export interface UserStats {
    prompts: {
        total: number;
        pending: number;
        processing: number;
        completed: number;
        failed: number;
    };
    responses: {
        total: number;
        completed: number;
    };
    queue_jobs: number;
}

export interface SystemStats {
    queue: {
        connection: string;
        total_pending_jobs: number;
    };
    timestamp: string;
}

export interface DashboardData {
    ollama: OllamaData;
    user_stats: UserStats;
    system: SystemStats;
}
