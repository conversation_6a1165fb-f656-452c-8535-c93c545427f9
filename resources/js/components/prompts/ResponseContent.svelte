<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { ChevronDown, ChevronRight, Brain } from 'lucide-svelte';
    import type { Response } from '@/types';

    interface Props {
        response: Response;
    }

    let { response }: Props = $props();
    
    let showReasoning = $state(false);

    // Use the separated fields from the response object
    const hasReasoning = $derived(!!response.reasoning);
    const reasoning = $derived(response.reasoning || '');
    const content = $derived(response.content || '');

    // Calculate reasoning line count for display
    const reasoningLineCount = $derived(
        reasoning ? reasoning.split('\n').length : 0
    );
</script>

<div class="space-y-3">
    <!-- Reasoning Section (colapsável) -->
    {#if hasReasoning}
        <div class="border rounded-lg bg-muted/30">
            <Button
                variant="ghost"
                size="sm"
                class="w-full justify-start p-3 h-auto font-normal"
                onclick={() => showReasoning = !showReasoning}
            >
                <div class="flex items-center gap-2 text-sm">
                    {#if showReasoning}
                        <ChevronDown class="h-4 w-4" />
                    {:else}
                        <ChevronRight class="h-4 w-4" />
                    {/if}
                    <Brain class="h-4 w-4 text-muted-foreground" />
                    <span class="text-muted-foreground">Reasoning</span>
                    <span class="text-xs text-muted-foreground/70">
                        ({reasoningLineCount} lines)
                    </span>
                </div>
            </Button>

            {#if showReasoning}
                <div class="px-3 pb-3">
                    <div class="prose prose-sm max-w-none dark:prose-invert">
                        <pre class="whitespace-pre-wrap text-sm bg-background/50 rounded p-3 border">{reasoning}</pre>
                    </div>
                </div>
            {/if}
        </div>
    {/if}

    <!-- Main Output -->
    {#if content}
        <div class="prose prose-sm max-w-none dark:prose-invert">
            <pre class="whitespace-pre-wrap text-sm">{content}</pre>
        </div>
    {/if}
</div>
