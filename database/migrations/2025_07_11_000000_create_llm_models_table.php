<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('llm_models', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name')->unique(); // Model name (e.g., "hf.co/unsloth/Qwen3-4B-GGUF:Q8_0")
            $table->string('display_name')->nullable(); // Human-readable name
            $table->text('description')->nullable();
            
            // Hugging Face metadata
            $table->string('huggingface_id')->nullable(); // e.g., "unsloth/Qwen3-4B-GGUF"
            $table->string('huggingface_url')->nullable();
            $table->json('huggingface_metadata')->nullable(); // Raw HF API response
            
            // Model capabilities
            $table->boolean('has_reasoning')->default(false); // Can think/reason
            $table->boolean('has_tool_usage')->default(false); // Can use external tools
            $table->boolean('has_vision')->default(false); // Can process images
            $table->boolean('has_code_generation')->default(false); // Can generate code
            $table->boolean('has_function_calling')->default(false); // Supports function calling
            
            // Technical specifications
            $table->bigInteger('context_window')->nullable(); // Context window size
            $table->string('parameter_count')->nullable(); // e.g., "4B", "7B", "70B"
            $table->bigInteger('parameter_count_numeric')->nullable(); // Numeric value for sorting
            $table->string('architecture')->nullable(); // e.g., "Qwen", "Llama", "Mistral"
            $table->string('quantization')->nullable(); // e.g., "Q8_0", "Q4_K_M", "FP16"
            $table->string('format')->nullable(); // e.g., "GGUF", "SafeTensors"
            
            // Chat and prompt formatting
            $table->string('chat_template')->nullable(); // Chat template format
            $table->json('prompt_templates')->nullable(); // Various prompt templates
            
            // Performance and resource requirements
            $table->bigInteger('model_size_bytes')->nullable(); // Model file size
            $table->integer('estimated_vram_gb')->nullable(); // Estimated VRAM requirement
            $table->decimal('benchmark_score', 8, 2)->nullable(); // Overall benchmark score
            $table->json('benchmark_details')->nullable(); // Detailed benchmark results
            
            // Licensing and usage
            $table->string('license')->nullable(); // Model license
            $table->boolean('commercial_use')->default(true); // Can be used commercially
            $table->json('tags')->nullable(); // Model tags/categories
            
            // Status and metadata
            $table->enum('status', ['active', 'inactive', 'deprecated'])->default('active');
            $table->timestamp('last_crawled_at')->nullable(); // Last time metadata was updated
            $table->timestamp('huggingface_updated_at')->nullable(); // Last update on HF
            $table->json('crawl_metadata')->nullable(); // Crawling status and errors
            
            $table->timestamps();
            
            // Indexes for better performance
            $table->index('name');
            $table->index('huggingface_id');
            $table->index(['status', 'created_at']);
            $table->index('parameter_count_numeric');
            $table->index(['has_reasoning', 'has_tool_usage', 'has_vision']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('llm_models');
    }
};
