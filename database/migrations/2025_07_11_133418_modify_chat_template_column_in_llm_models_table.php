<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('llm_models', function (Blueprint $table) {
            // Change chat_template from string (VARCHAR 255) to text (TEXT)
            $table->text('chat_template')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('llm_models', function (Blueprint $table) {
            // Revert back to string with 255 character limit
            $table->string('chat_template')->nullable()->change();
        });
    }
};
