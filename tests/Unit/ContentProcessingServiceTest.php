<?php

namespace Tests\Unit;

use App\Services\ContentProcessingService;
use PHPUnit\Framework\TestCase;

class ContentProcessingServiceTest extends TestCase
{
    private ContentProcessingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ContentProcessingService();
    }

    public function test_processes_content_without_reasoning_tags()
    {
        $content = "This is a simple response without any reasoning.";
        
        $result = $this->service->processResponseContent($content);
        
        $this->assertNull($result['reasoning']);
        $this->assertEquals($content, $result['content']);
    }

    public function test_processes_content_with_single_reasoning_block()
    {
        $content = "<think>\nThis is my reasoning process.\nI need to think about this carefully.\n</think>\n\nThis is the final answer.";
        
        $result = $this->service->processResponseContent($content);
        
        $this->assertEquals("This is my reasoning process.\nI need to think about this carefully.", $result['reasoning']);
        $this->assertEquals("This is the final answer.", $result['content']);
    }

    public function test_processes_content_with_multiple_reasoning_blocks()
    {
        $content = "<think>\nFirst reasoning block.\n</think>\n\nSome content here.\n\n<think>\nSecond reasoning block.\n</think>\n\nFinal answer.";
        
        $result = $this->service->processResponseContent($content);
        
        $this->assertEquals("First reasoning block.\n\nSecond reasoning block.", $result['reasoning']);
        $this->assertEquals("Some content here.\n\nFinal answer.", $result['content']);
    }

    public function test_processes_empty_content()
    {
        $result = $this->service->processResponseContent('');
        
        $this->assertNull($result['reasoning']);
        $this->assertEquals('', $result['content']);
    }

    public function test_processes_content_with_empty_reasoning_tags()
    {
        $content = "<think></think>\n\nThis is the answer.";
        
        $result = $this->service->processResponseContent($content);
        
        $this->assertNull($result['reasoning']);
        $this->assertEquals("This is the answer.", $result['content']);
    }

    public function test_has_reasoning_tags_detection()
    {
        $this->assertTrue($this->service->hasReasoningTags('<think>Some reasoning</think>'));
        $this->assertTrue($this->service->hasReasoningTags('Text before <think>reasoning</think> text after'));
        $this->assertFalse($this->service->hasReasoningTags('No reasoning tags here'));
        $this->assertFalse($this->service->hasReasoningTags(''));
    }

    public function test_reconstructs_full_content()
    {
        $reasoning = "This is my reasoning";
        $content = "This is the answer";
        
        $result = $this->service->reconstructFullContent($reasoning, $content);
        
        $this->assertEquals("<think>\n{$reasoning}\n</think>\n\n{$content}", $result);
    }

    public function test_reconstructs_content_without_reasoning()
    {
        $content = "This is the answer";
        
        $result = $this->service->reconstructFullContent(null, $content);
        
        $this->assertEquals($content, $result);
    }

    public function test_validates_reasoning_content()
    {
        $this->assertTrue($this->service->validateReasoningContent('Valid reasoning content'));
        $this->assertFalse($this->service->validateReasoningContent('Invalid <think> nested tags'));
        $this->assertFalse($this->service->validateReasoningContent('Invalid </think> closing tag'));
    }

    public function test_handles_case_insensitive_tags()
    {
        $content = "<THINK>\nUppercase reasoning.\n</THINK>\n\nAnswer here.";
        
        $result = $this->service->processResponseContent($content);
        
        $this->assertEquals("Uppercase reasoning.", $result['reasoning']);
        $this->assertEquals("Answer here.", $result['content']);
    }

    public function test_handles_whitespace_around_tags()
    {
        $content = "  <think>  \n  Reasoning with whitespace  \n  </think>  \n\n  Answer with whitespace  ";
        
        $result = $this->service->processResponseContent($content);
        
        $this->assertEquals("Reasoning with whitespace", $result['reasoning']);
        $this->assertEquals("Answer with whitespace", $result['content']);
    }
}
