<?php

namespace Tests\Feature;

use App\Models\Prompt;
use App\Models\Response;
use App\Models\User;
use App\Services\ContentProcessingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ResponseContentSeparationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Prompt $prompt;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->prompt = Prompt::factory()->create([
            'user_id' => $this->user->id,
            'content' => 'Test prompt content'
        ]);
    }

    public function test_response_can_store_separated_reasoning_and_content()
    {
        $reasoning = "I need to think about this step by step.\nFirst, I'll analyze the problem.";
        $content = "Here is my final answer based on the reasoning above.";

        $response = Response::create([
            'prompt_id' => $this->prompt->id,
            'model' => 'test-model',
            'content' => $content,
            'reasoning' => $reasoning,
            'status' => 'completed',
            'generated_at' => now(),
        ]);

        $this->assertDatabaseHas('responses', [
            'id' => $response->id,
            'content' => $content,
            'reasoning' => $reasoning,
        ]);

        $this->assertTrue($response->hasReasoning());
        $this->assertEquals($reasoning, $response->reasoning);
        $this->assertEquals($content, $response->content);
    }

    public function test_response_can_store_content_without_reasoning()
    {
        $content = "Simple answer without reasoning.";

        $response = Response::create([
            'prompt_id' => $this->prompt->id,
            'model' => 'test-model',
            'content' => $content,
            'reasoning' => null,
            'status' => 'completed',
            'generated_at' => now(),
        ]);

        $this->assertDatabaseHas('responses', [
            'id' => $response->id,
            'content' => $content,
            'reasoning' => null,
        ]);

        $this->assertFalse($response->hasReasoning());
        $this->assertNull($response->reasoning);
        $this->assertEquals($content, $response->content);
    }

    public function test_response_full_content_attribute_reconstructs_original()
    {
        $reasoning = "My reasoning process";
        $content = "My final answer";

        $response = Response::create([
            'prompt_id' => $this->prompt->id,
            'model' => 'test-model',
            'content' => $content,
            'reasoning' => $reasoning,
            'status' => 'completed',
            'generated_at' => now(),
        ]);

        $expectedFullContent = "<think>\n{$reasoning}\n</think>\n\n{$content}";
        $this->assertEquals($expectedFullContent, $response->full_content);
    }

    public function test_response_full_content_attribute_without_reasoning()
    {
        $content = "Simple answer";

        $response = Response::create([
            'prompt_id' => $this->prompt->id,
            'model' => 'test-model',
            'content' => $content,
            'reasoning' => null,
            'status' => 'completed',
            'generated_at' => now(),
        ]);

        $this->assertEquals($content, $response->full_content);
    }

    public function test_content_processing_service_integration()
    {
        $service = app(ContentProcessingService::class);
        
        $originalContent = "<think>\nThis is my reasoning.\nI need to be careful here.\n</think>\n\nThis is my final answer.";
        
        $processed = $service->processResponseContent($originalContent);
        
        $response = Response::create([
            'prompt_id' => $this->prompt->id,
            'model' => 'test-model',
            'content' => $processed['content'],
            'reasoning' => $processed['reasoning'],
            'status' => 'completed',
            'generated_at' => now(),
        ]);

        $this->assertEquals("This is my reasoning.\nI need to be careful here.", $response->reasoning);
        $this->assertEquals("This is my final answer.", $response->content);
        
        // Verify we can reconstruct the original
        $reconstructed = $service->reconstructFullContent($response->reasoning, $response->content);
        $this->assertEquals($originalContent, $reconstructed);
    }

    public function test_api_response_includes_reasoning_field()
    {
        $reasoning = "API test reasoning";
        $content = "API test content";

        $response = Response::create([
            'prompt_id' => $this->prompt->id,
            'model' => 'test-model',
            'content' => $content,
            'reasoning' => $reasoning,
            'status' => 'completed',
            'generated_at' => now(),
        ]);

        $this->actingAs($this->user);
        
        $apiResponse = $this->getJson("/api/prompts/{$this->prompt->uuid}");
        
        $apiResponse->assertStatus(200);
        
        $responseData = $apiResponse->json('data.responses.0');
        $this->assertEquals($content, $responseData['content']);
        $this->assertEquals($reasoning, $responseData['reasoning']);
    }
}
